import { Constant, FREE_TYPE, GameEvent, GameTextTips, LINE_POS_EX, Protos, updateGameTextTips} from "./SLOT777Define";
import GameCore from "../../../../script/frame/model/GameCore";
import Common from "../../../../script/frame/common/Common";
import AlertHelper from "../../../../script/frame/extentions/AlertHelper";
import UIHelper from "../../../../script/frame/extentions/UIHelper";
import SLOT777GameView from "../view/SLOT777GameView";
import EventManager from "../../../../script/frame/manager/EventManager";
import SLOT777LotteryBox from "../view/SLOT777LotteryBox";
import Config from "../../../../script/frame/config/Config";
import { TextTips } from "../../../../script/frame/common/Language";


/**滚动数值 */
export type ScrollNumberParam = {
    /**文本节点 */
    txt: cc.Label
    /**起始值 */
    began: number
    /**结束值 */
    end: number
    /**滚动次数 */
    nCount?: number
    /**滚动时长 */
    nTime?: number
    /**滚动间隔 */
    nInterval?: number
    /**延时启动 */
    nDelay?: number
    /**不节点停止动作 */
    bNotStopAction?: boolean
    /**自己格式化 */
    format?: (nValue: number, data: ScrollNumberParam) => void
    /**每次更新 */
    update?: (data: ScrollNumberParam) => void
    /**回调 */
    callback?: (data: ScrollNumberParam) => void
}

/**水果机元素 */
export type FruitMachineBaseMachineElement = {
    /**当前节点 */
    node: cc.Node
    /**所在行[0, 4] */
    nRow?: number
    /**所在列[0, 4] */
    nCol?: number
    /**元素类型[1, 11] */
    nType?: number
    /**转圈动画 */
    animCircle?: cc.Node,
    //停止行
    nStopRow?: number,
    //是否停止好了
    isOK?: boolean,
    //移动位置
    nMoveIndex?: number,
    //缩放
    nScale?: number
}

/**中奖的线信息 */
export type WinLinesInfo = {
    /**线（UI界面中的线索引） */
    nLine: number,
    /**元素 */
    nType?: number,
    /**中奖元素个数 */
    nCount?: number,
    /**倍率 */
    nMultiple?: number,
    /**中奖图标 3行5列 按一维数组 1~15 区分*/ 
    prizeIconList?: number[];
}

/**水果机摇奖状态 */
export enum SpinStateFruitMachine {
	/**未初始化 */
	None = 0,
	/**正常 */
	Normal,
	/**免费 */
	Free,
}

/**桌上玩家信息 */
export type DeskPlayersInfo = {
    /**当前玩家节点 */
    node: cc.Node,
    /**昵称金币父节点 */
    infoBgNode?: cc.Node,
    /**玩家昵称 */
    nameNode?: cc.Node,
    /**玩家金币 */
    goldNode?: cc.Node,
    /**头像底 */
    headBgNode?: cc.Node,
    /**头像 */
    headNode?: cc.Node,
    /**头像转圈动画 */
    headSpinAniNode?: cc.Node,
    /**其它动画 */
    aniNode?: cc.Node,
    /**输赢飘分 */
    winNode?: cc.Node,
    /**飘分底图 */
    winBgNode?: cc.Node,
    /**飘分 */
    winOrLoseNumNode?: cc.Node,
    /*玩家id*/
    playerid?:number,
    /*是否在播放动画标志*/
    playingAni?:boolean,
    /*玩家身上金币*/
    curMoney?: number
}

//////////////////////////////////////////////////////////////////////////////////
const { ccclass, property,disallowMultiple,menu } = cc._decorator;
@ccclass
@disallowMultiple()
@menu('slot777/SLOT777GameCore')
export default class SLOT777GameCore extends GameCore {

    //////////////////////////////////////////////////////////////////////////////
    // 游戏视图对象
    gameView: SLOT777GameView = null;
    //lotterBox对象
    lotteryBox: SLOT777LotteryBox = null;

    // 下注金额
    betMoney: number = 0;
    // 自己下注索引
    meAddBeiLv: number = 0;
    //下注实际值 = 底分 * 下注倍数 * 固定倍率值9 
    bets = [];
    //下注倍数列表
    betsOdds = [];
    // 底数
    difen: number = 0;
    //上次使用的奖池金额值
    jackpotnum: number = 0;
    //原奖池金额
    nOldJackpotNum: number = null;
    //正常开奖结果
    resultData: any = null;
     //免费开奖结果
    resultFreeData: any = null;
	/**水果机元素 [行，从0开始][列，从0开始] 二维数组 */
	machineElement: FruitMachineBaseMachineElement[][];
	machineElementMap: Map<cc.Node, FruitMachineBaseMachineElement> = new Map();
    /**摇奖按钮状态 */
	SpinState = SpinStateFruitMachine;
    /**摇奖按钮状态 */
	nSpinState: number = SpinStateFruitMachine.Normal;
    /**自动摇是否开启 */
	bAutoSpin: boolean = false;
    /**水果机是否停止 */
	bFruitIsStop: boolean = true;
    /**当前播放的线 */
    curLineIndex: number = 0;
    /**摇奖元素信息 */
	fruitData: number[][] = [[],[],[]];
    //滚动值
    m_RollNum = [0,0,0,0,0]
    //回退值
    m_springback = [0,0,0,0,0]
    //移动步数
    m_nDoLen = [30, 30, 30, 30, 30];
    //免费总赢金额（未扣税显示用）
    freeWinTotalNum: number = 0;
    //免费总赢金额（扣税后）
    freeChangeTotalNum: number = 0;
    //记录上一次赢的金额
    lastFreeWinNum: number = 0;
    /**本次免费摇奖次数 */
	nLastFreeTimes: number = 0;
    /**上次免费摇奖次数 */
	nLastMaxFreeTimes: number = 0;
    /**免费摇结束 */
    freeTimesEndFlag = FREE_TYPE.non;
    //其它玩家jackpot
    otherJackpotList = [];
    //自己最终实时余额
    myselfMoney: number = 0;
    //游戏总金币
    playScoreMoney = 0;
    //滚动写死的一个位置列表
    m_indexMovePosList = [];
    //定时器计数
    m_updateIndex = 0;
    //桌上玩家信息
    deskPlayerInfoList: DeskPlayersInfo[] = [];
    //上次免费之前是否自动状态
    lastAutoSpinState: boolean = false;
  
    //////////////////////////////////////////////////////////////////////////////

    onLoad() {
        this.gameView = this.node.getComponent("SLOT777GameView");
        this.lotteryBox = this.node.getComponent("SLOT777LotteryBox"); 
        this.updateGameLan();
        super.onLoad();
        
    }

    private updateGameLan() {
        updateGameTextTips();
        TextTips["GameTextTips"] = GameTextTips;
    }

    //////////////////////////////////////////////////////////////////////////////

    // jackpot记录
    public sendJackpotRecord() {
        this.sendGameMessage(Protos.CS_SLOT777_JPLIST_P);
    }


    // 玩家下注
    public sendGameStart() {
        this.sendGameMessage(Protos.CS_SLOT777_GAMESTART_P, { odds: this.betsOdds[this.meAddBeiLv] });
    }

    //加注
    public onBetSub(){
        this.meAddBeiLv -= 1;
        if(this.meAddBeiLv < 0){
            this.meAddBeiLv = this.bets.length - 1;
        }
        this.betMoney = this.bets[`${this.meAddBeiLv}`] * Config.SCORE_RATE;;
    }

    //减注
    public onBetAdd(){
        this.meAddBeiLv += 1;
        if(this.meAddBeiLv == this.bets.length){
            this.meAddBeiLv = 0;
        }
        this.betMoney = this.bets[`${this.meAddBeiLv}`] * Config.SCORE_RATE;;
    }    

    // 玩家退出游戏
    public quitGame(info?: any) {
        this.updatePlayerMoney({coin:this.myselfMoney,playerid:this.playerid})
        if (!info && !this.bFruitIsStop) {
            AlertHelper.confirm(GameTextTips.QUIT_TIPS, super.quitGame.bind(this), null);
        }
        else {
            UIHelper.clearAll();
            super.quitGame(info);
        }
    }

    //////////////////////////////////////////////////////////////////////////////
    public start() {
        this.bindGameMessage(Protos.SC_SLOT777_JPLIST_P, this.onGetJackpotRecord,this);
        this.bindGameMessage(Protos.SC_SLOT777_GAMESTART_P, this.onStartGame,this);
        this.bindGameMessage(Protos.SC_SLOT777_FREEGAME_P, this.onStartFreeGame,this);
        this.bindGameMessage(Protos.SC_SLOT777_JACKPOT_P, this.onUpdateJackpot,this);
        this.bindGameMessage(Protos.SC_SLOT777_JPAWARD_P, this.onOhterPalyerJackpot,this);
        this.bindGameMessage(Protos.SC_SLOT777_PLAYERLIST_P, this.onUpdateDeskPlayersInfo,this);
        this.bindGameMessage(Protos.SC_SLOT777_GAMERESULT_P, this.onUpdateDeskPlayersResultInfo,this);
        EventManager.instance.on(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        EventManager.instance.on(GameEvent.ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE, this.rechargeToUpdateMyselfCoid, this);
        super.start();
    }

    public exit() {
        this.unbindGameMessage(Protos.SC_SLOT777_JPLIST_P, this.onGetJackpotRecord,this);
        this.unbindGameMessage(Protos.SC_SLOT777_GAMESTART_P, this.onStartGame,this);
        this.unbindGameMessage(Protos.SC_SLOT777_FREEGAME_P, this.onStartFreeGame,this);
        this.unbindGameMessage(Protos.SC_SLOT777_JACKPOT_P, this.onUpdateJackpot,this);
        this.unbindGameMessage(Protos.SC_SLOT777_JPAWARD_P, this.onOhterPalyerJackpot,this);
        this.unbindGameMessage(Protos.SC_SLOT777_PLAYERLIST_P, this.onUpdateDeskPlayersInfo,this);
        this.unbindGameMessage(Protos.SC_SLOT777_GAMERESULT_P, this.onUpdateDeskPlayersResultInfo,this);
        EventManager.instance.off(GameEvent.HALL_SWITCH_LANGUAGE, this.updateGameLan, this);
        EventManager.instance.off(GameEvent.ACCOUNT_GAMECHARGE_DRAWDOWM_NOTICE, this.rechargeToUpdateMyselfCoid, this);
        TextTips["GameTextTips"] = {};
        super.exit();
    }

    // 玩家加入
    public onPlayerEnter(info: any) {
        super.onPlayerEnter(info);
        //Common.dump(info,"onPlayerEnter");
    }

    // 玩家离开
    public onPlayerQuit(info: any) {
        super.onPlayerQuit(info);
        //Common.dump(info,"onPlayerQuit");
    }

    // 游戏结束,你条件不满足被踢出房间,如果你在暂离状态,也会被踢出房间
    public onDeletePlayer(info: any) {
        super.onDeletePlayer(info);
    }

    // 玩家状态
    public onPlayerState(info: any) {
        super.onPlayerState(info);

        //Common.dump(info,"onPlayerState");
    }

    // 更新玩家金币
    public updatePlayerMoney(info: any) {
        super.updatePlayerMoney(info);
        if (info.playerid == this.playerid) {
            if (info["coin"]) {
                this.myselfMoney = info["coin"];
            }
        }
    }

    //充值更新余额
    rechargeToUpdateMyselfCoid(){
        console.error('充值更新余额',this.myselfMoney)
        this.playScoreMoney = this.myselfMoney;
        this.gameView.showSelfMoney();
    }

    // 进入房间，房间信息
    public onRoomInfo(info: any) {
        super.onRoomInfo(info);
        //Common.dump(info,"onRoomInfo");

        // 重置游戏界面与玩家数据
        this.gameView.resetAllUI();
        // this.onGameConfig(info)
        // 玩家信息
        let playerlist = info["playerlist"];
        if (playerlist && typeof(playerlist) == "object") {
            // 先找出自己的位置
            for (let key in playerlist) {
                let plyInfo = playerlist[key];
                let playerid = Common.toInt(plyInfo["playerid"]);
                if (playerid == this.playerid) {
                    this.mySeatId = Common.toInt(plyInfo["seat"]);
                    break;
                }
            }
            // 显示所有玩家
            for (let key in playerlist) {
                let plyInfo = playerlist[key];
                let playerid = Common.toInt(plyInfo["playerid"]);
                let seat = Common.toInt(plyInfo["seat"]);

                // 玩家是否在游戏中  单机游戏只显示自己
                if (playerid == this.playerid) {
                    if (plyInfo["money"]) {
                        this.playScoreMoney = plyInfo["money"];
                        this.myselfMoney = this.playScoreMoney;
                        this.gameView.showSelfMoney();
                    }
                    break;
                }
            } 
        }
    }

    // 房间状态
    public onRoomState(info: any) {
        super.onRoomState(info);
        //Common.dump(info,"onRoomState");
    }

    // 房间信息(断线重入)
    public onToOtherRoom(info: any) {
        super.onToOtherRoom(info);
        //Common.dump(info,"onToOtherRoom");
        this.onGameConfig(info);
        this.gameView.onToOtherRoom();
    }

    //////////////////////////////////////////////////////////////////////////////
    //游戏主逻辑

    //参数1："jackpot" //当前Jackpot的值
    //更新jackpot奖池
    onUpdateJackpot(info: any){
        if(!info){
            return;
        }
         //jackpot奖池
         let jackpot =Number(this.gameView.moneyFormat(Number(info["jackpot"]??0)));
         this.gameView.updateJackpotNum(jackpot);
         EventManager.instance.emit(GameEvent.JACKPOT_RECORD,info);    
    }   

    /*
        SC_SLOT777_JPAWARD_P //响应-Jackpot中奖通知
        参数1："playerid" //玩家ID
        参数2："name" //玩家昵称
        参数3："headid" //玩家系统头像id
        参数4："wxheadurl" //自定义头像url
        参数5："winscore" //玩家赢的金币	
    */
    //其它玩家中奖
    onOhterPalyerJackpot(info: any){
        if(!info){
            return;
        }
        if(info.playerid == this.playerid){ //自己
            return;
        }
        this.otherJackpotList.push(info);
        
        this.gameView.playerOhterJackpotAni();
    }

    //jackpot记录
    onGetJackpotRecord(info: any){
        // Common.dump(info,'onGetJackpotRecord');
        EventManager.instance.emit(GameEvent.JACKPOT_RECORD_INFO,info);    
    }

    // 游戏消息-配置
    private onGameConfig(info: any) {
        //Common.dump(info,"onGameConfig");

        //底分
         this.difen = Number(this.gameView.moneyFormat(info["difen"],2));
                 
         //jackpot奖池
         let jackpot =Number(this.gameView.moneyFormat(Number(info["jackpot"]??0)));
         this.gameView.updateJackpotNum(jackpot);

        //配置筹码
        let bets = info["odds"];
        for(let i in bets){
            this.bets.push(bets[`${i}`] * Constant.BET_RATE_NUM * this.difen);
            this.betsOdds.push(bets[`${i}`]);
        }
        if(this.bets.length == 0){//给个默认筹码列表
            this.bets = Constant.BET_NUM_CONFIG_LIST;
        }

        if(info["lastodds"]){
            for(let i in bets){
                if(bets[`${i}`] == info["lastodds"]){
                    this.meAddBeiLv = Number(i) - 1;
                }
            }
        }
        
        //默认最低下注金额 本地如果有正确值,则默认本地值
        // let localBetNum = cc.sys.localStorage.getItem(Constant.LOCAL_STORAGE_BET_NUM_KEY);
        // if(!Common.isNull(localBetNum)){//有效配置值
        //     this.bets.forEach((value,index)=>{
        //         if(value == Number(localBetNum)){
        //             this.meAddBeiLv = index;
        //         }
        //     })
        // }
        //默认选择的下注金额
        this.betMoney = this.bets[this.meAddBeiLv] * Config.SCORE_RATE;
        this.gameView.showBetConfig();

        if(info["playerlist"]){
            if(cc.winSize.width/cc.winSize.height >= 2){
                this.gameView.onUpdateDeskPlayersInfo(info["playerlist"]);
            }
        }
    }

    //接收通用道具信息
    public onRoomChat(info: any) {
        console.log("onRoomChat", info);
        let sendPlayerid = Common.toInt(info["sendPlayerid"]);
        let receiverPlayerid = Common.toInt(info["receiverPlayerid"]);
        // let type = info["type"];
        let content = info["content"];

        let sendPlayerPos = this.gameView.getHeadPosAtPlayerId(sendPlayerid);//发送者位置
        let receiverPlayerPos = this.gameView.getHeadPosAtPlayerId(receiverPlayerid,true);//接收者位置

        if(Common.isNull(sendPlayerPos) || Common.isNull(receiverPlayerPos)){
            return;
        }
    
        this.gameView.doRoomIntertChat(sendPlayerPos, receiverPlayerPos, content);
    }

    /*
        参数1：数组返回结果
        参数1："playerid" //玩家ID
        参数2："name" //玩家昵称
        参数3："headid" //玩家系统头像id
        参数4："wxheadurl" //自定义头像url
        参数5："pmoney" //玩家金币
    */
    //更新桌上玩家信息
    onUpdateDeskPlayersInfo(info){
        // console.error('===玩家信息===')
        // console.error(info)
        if(!info){
            return;
        }
        
        if(cc.winSize.width/cc.winSize.height < 2){
            return;
        }

        this.gameView.onUpdateDeskPlayersInfo(info);
    }

    /*
        参数1："playerid" //玩家ID
        参数2："winscore" //赢的分数
        参数3："mult" //赢的倍数
        参数4："pmoney" //玩家金币
    */
    //更新桌上玩家游戏结果
    onUpdateDeskPlayersResultInfo(info){
        // console.error('===游戏结果===')
        // console.error(info)
        if(!info){
            return;
        }
        
        if(cc.winSize.width/cc.winSize.height < 2){
            return;
        }

        this.gameView.onUpdateDeskPlayersResultInfo(info);
    }

    /*
        SC_SLOT777_GAMESTART_P //响应-游戏开始 免费相同结构,但是多个数组结构 
        参数1："freetimes" //免费次数
        参数2："sevennum" //7的个数
        参数3："iconresult" //游戏图标集合(3x5数组,值对应EM_SLOT777_ICONTYPE)
        参数4："linecount" //命中的线条个数
        参数5："lineresult" //命中的线条(数组)
                参数1："line" //线条索引(1-9号线)
                参数2："num" //命中的数量(列数)
        参数6："totalmult" //命中线条赢得总倍数 
        参数7："winmoney" //命中线条赢得的总金额
        参数8："changemoney" //玩家的输赢分
        参数9："jackpotcash" //中Jackpot获得的金额(未中时为0)
        参数13："luckyjackpot" //转盘彩金
    */

    //免费游戏开始
    onStartFreeGame(info: any){
        // console.log('免费开始..........');
        // console.error(info)
        this.resultFreeData = info;

        if(!this.resultFreeData || this.resultFreeData.length == 0){
            console.error('onStartFreeGame.....info is null');
            return;
        }
    }

    // 游戏开始
    onStartGame(info: any) {
         console.log('正常开始..........');
         console.log('游戏结果数据:', info);

        this.resultData = info;
        this.resultFreeData = null;
        this.freeWinTotalNum = 0;
        this.freeChangeTotalNum = 0;
        this.lastFreeWinNum = 0;

        if(!this.resultData){
            console.error('onStartGame.....info is null');
            return;
        }

        // // 处理服务器返回的最新金币数据
        // if(this.resultData["current_money"] !== undefined) {
        //     this.playScoreMoney = this.resultData["current_money"];
        //     this.myselfMoney = this.playScoreMoney;
        //     console.log('更新玩家金币:', this.playScoreMoney);
        //     this.gameView.showSelfMoney();
        // }

        //免费次数
        this.nLastFreeTimes = this.resultData.freetimes;
        this.nLastMaxFreeTimes = this.resultData.freetimes;
        this.freeTimesEndFlag = FREE_TYPE.non;

        this.gameView.runNotBetToQuick();
        //开始游戏
        this.lotteryBox.startGame();

    }

    //设置结算相关信息
    setResultDataInfo(){
        this.fruitData = [[],[],[]];
        
        this.lotteryBox.resetData();
        //解析结果值  变成3行5列数组
        for(let key in this.resultData.iconresult){
            this.fruitData[Math.floor((Number(key) - 1) / Constant.COL_SHOW)].push(this.resultData.iconresult[key]);
        }
        
        //存到线条对应对象中
        this.resultData.winLinesInfo = [];
        if(!Common.isNull(this.resultData.linecount) && this.resultData.linecount > 0){
            for(let key in this.resultData.lineresult){
                let subWinLinesInfo: WinLinesInfo = <WinLinesInfo>{}
                let lineInfo = this.resultData.lineresult[key];
                subWinLinesInfo.nLine = lineInfo.line;//中奖线
                subWinLinesInfo.nCount = lineInfo.num;//该线中奖个数

                //从对应线中取对应中奖的索引位置
                let prizeIconList = [];
                for(let j = 0; j < LINE_POS_EX[subWinLinesInfo.nLine - 1].length;j++){
                    if(j < subWinLinesInfo.nCount){
                        let prizeIndex = LINE_POS_EX[subWinLinesInfo.nLine - 1][j];
                        prizeIconList.push(prizeIndex);
                    }
                }
                subWinLinesInfo.prizeIconList = prizeIconList.sort((a,b)=>{return a - b});
                this.resultData.winLinesInfo.push(subWinLinesInfo);
            }
        }
        this.resultData.multgoldnum = this.resultData.totalmult / 10;//倍数除以10 

        //免费不扣 - 积分更新已在onStartGame中处理，这里不再重复扣除
        // 注释掉重复的扣款逻辑，因为服务器已经计算好了最终金币
        if(this.freeTimesEndFlag == FREE_TYPE.non){
            // 扣除金币
            cc.log("playScoreMoney",this.betMoney)
            this.playScoreMoney -= this.betMoney;
            this.gameView.showSelfMoney();
        }
    }

    //重置数值
    resetDataValue() {
        this.m_RollNum = [0, 0, 0, 0, 0];
        this.m_springback = [0, 0, 0, 0, 0];
        this.m_nDoLen = [30, 30, 30, 30, 30];

        this.m_indexMovePosList = [];
        for(let nRow = 0;nRow < Constant.ROW_MAX;nRow++){
            let list = [];
            for(let nCol = 0;nCol < Constant.COL_SHOW;nCol++){
                list.push(nCol * 5 + 2);
            }
            this.m_indexMovePosList.push(list);
        }
        this.m_updateIndex = 0;
    }
}
